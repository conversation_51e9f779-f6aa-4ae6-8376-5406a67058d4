# Multi-stage build for BSE Scraper
# Stage 1: Build stage
FROM python:3.11-slim as builder

# Set working directory
WORKDIR /app

# Install system dependencies for building
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
COPY bse_scraper/requirements.txt ./bse_requirements.txt

# Install dependencies
RUN pip install --no-cache-dir --user -r requirements.txt
RUN pip install --no-cache-dir --user -r bse_requirements.txt

# Stage 2: Runtime stage
FROM python:3.11-slim

# Create non-root user for security
RUN groupadd -r scraper && useradd -r -g scraper scraper

# Set working directory
WORKDIR /app

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Copy Python packages from builder stage
COPY --from=builder /root/.local /home/<USER>/.local

# Copy application code
COPY shared/ ./shared/
COPY bse_scraper/ ./bse_scraper/
COPY proxy_config.py ./

# Create output directories
RUN mkdir -p /app/bse_data \
    && chown -R scraper:scraper /app

# Switch to non-root user
USER scraper

# Add local Python packages to PATH
ENV PATH=/home/<USER>/.local/bin:$PATH
ENV PYTHONPATH=/app

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import sys; sys.exit(0)"

# Set entrypoint and default command for Cloud Run
ENTRYPOINT ["python", "-m", "bse_scraper.main"]
CMD ["--days", "1"]
