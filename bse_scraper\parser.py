"""
HTML/CSV Parser for BSE Data
Extracts insider trading data from BSE HTML responses and CSV downloads.
Based on the working bsescraper.py implementation.
"""

import pandas as pd
from bs4 import BeautifulSoup
import re
import io


class BSEParser:
    """
    Parses HTML content and CSV data from BSE to extract insider trading data.
    Handles both web page tables and downloadable CSV files.
    """

    def __init__(self, session_manager=None):
        """Initialize the parser"""
        self.soup = None
        self.session_manager = session_manager
        self.start_date = None
        self.end_date = None
    
    def parse_html_content(self, html_content, start_date=None, end_date=None):
        """
        Main method to parse HTML and extract data.
        Handles both HTML tables and CSV content.
        Returns pandas DataFrame or None if parsing fails.
        """
        # Store date range for download functionality
        self.start_date = start_date
        self.end_date = end_date
        try:
            # Check if content looks like CSV
            if self.is_csv_content(html_content):
                print("Detected CSV content, parsing as CSV...")
                return self.parse_csv_content(html_content)

            # Parse HTML with BeautifulSoup
            self.soup = BeautifulSoup(html_content, 'html.parser')

            # Check for download link first (BSE often provides download option)
            download_result = self.try_download_all_records()
            if download_result is not None and not download_result.empty:
                print(f"✅ Successfully downloaded complete dataset: {len(download_result)} records")
                return download_result

            # Fall back to parsing HTML table
            print("Parsing HTML table data...")
            table = self.soup.find("table", {"id": "ContentPlaceHolder1_gvData"})

            if not table:
                print("❌ BSE data table not found. Checking for error messages...")
                error_div = self.soup.find("div", {"id": "ContentPlaceHolder1_lblError"})
                if error_div and error_div.text.strip():
                    print(f"BSE server error: {error_div.text.strip()}")
                return None

            print("✅ Found BSE data table")
            return self.parse_bse_table(table)

        except Exception as e:
            print(f"Error parsing HTML content: {e}")
            return None

    def is_csv_content(self, content):
        """Check if content appears to be CSV format"""
        # Check first 200 characters for CSV indicators
        content_start = content[:200].lower()
        return (',' in content_start and
                ('security code' in content_start or
                 'security_code' in content_start or
                 'company' in content_start))

    def parse_csv_content(self, content):
        """Parse CSV content into DataFrame"""
        try:
            # Try different encodings
            encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']

            for encoding in encodings:
                try:
                    if isinstance(content, str):
                        content_bytes = content.encode(encoding)
                    else:
                        content_bytes = content

                    df = pd.read_csv(io.BytesIO(content_bytes), encoding=encoding)
                    print(f"✅ Successfully parsed CSV with {len(df)} records using {encoding}")
                    return self.clean_bse_dataframe(df)
                except UnicodeDecodeError:
                    continue

            print("❌ Failed to parse CSV with any encoding")
            return None

        except Exception as e:
            print(f"Error parsing CSV content: {e}")
            return None
    
    def find_all_tables(self):
        """Find all table elements in the HTML"""
        tables = self.soup.find_all('table')
        print(f"Found {len(tables)} tables in HTML")
        return tables
    
    def try_download_all_records(self):
        """Try to find and trigger download of all records"""
        try:
            download_link = self.soup.find("a", {"id": "ContentPlaceHolder1_lnkDownload"})
            if download_link:
                print("📥 Found 'Download All Records' link")

                # If we have a session manager, try to trigger the download
                if self.session_manager:
                    return self._trigger_download(download_link)
                else:
                    print("⚠️ No session manager available for download")
                    return None
            else:
                print("📥 No download link found")
                return None
        except Exception as e:
            print(f"Error checking for download link: {e}")
            return None

    def _trigger_download(self, download_link):
        """Trigger the actual download of all records"""
        try:
            # Extract the download URL or JavaScript function
            href = download_link.get('href', '')
            onclick = download_link.get('onclick', '')

            print(f"📥 Download link href: {href}")
            print(f"📥 Download link onclick: {onclick}")

            # Handle different types of download links
            if href and href.startswith('javascript:'):
                # JavaScript-based download (most common for BSE)
                return self._handle_javascript_download(href)
            elif onclick and 'javascript:' in onclick:
                # JavaScript-based download from onclick
                return self._handle_javascript_download(onclick)
            elif href and href.startswith('http'):
                # Direct download URL
                return self._download_direct_url(href)
            elif href and not href.startswith('#') and not href.startswith('javascript:'):
                # Relative URL
                base_url = "https://www.bseindia.com"
                full_url = base_url + href if not href.startswith('/') else base_url + href
                return self._download_direct_url(full_url)
            else:
                print("❌ Could not determine download method")
                return None

        except Exception as e:
            print(f"Error triggering download: {e}")
            return None

    def _download_direct_url(self, url):
        """Download data from a direct URL"""
        try:
            print(f"📥 Attempting to download from: {url}")
            response = self.session_manager.make_get_request(url)

            if response and response.status_code == 200:
                print(f"✅ Successfully downloaded data ({len(response.content)} bytes)")

                # Try to parse as CSV
                content_type = response.headers.get('content-type', '').lower()
                if 'csv' in content_type or url.endswith('.csv'):
                    return self.parse_csv_content(response.text)
                else:
                    # Try to parse as CSV anyway
                    csv_result = self.parse_csv_content(response.text)
                    if csv_result is not None:
                        return csv_result

                    # If CSV parsing fails, try HTML parsing
                    return self.parse_html_content(response.text)
            else:
                print(f"❌ Download failed with status: {response.status_code if response else 'None'}")
                return None

        except Exception as e:
            print(f"Error downloading from URL: {e}")
            return None

    def _handle_javascript_download(self, onclick):
        """Handle JavaScript-based download triggers"""
        try:
            print("📥 Handling JavaScript download...")

            # Extract parameters from JavaScript function
            # Common patterns: __doPostBack('ctl00$ContentPlaceHolder1$lnkDownload','')
            if '__doPostBack' in onclick:
                return self._handle_postback_download(onclick)
            else:
                print("❌ Unsupported JavaScript download method")
                return None

        except Exception as e:
            print(f"Error handling JavaScript download: {e}")
            return None

    def _handle_postback_download(self, onclick):
        """Handle ASP.NET postback download"""
        try:
            print("📥 Handling ASP.NET postback download...")

            # Extract postback parameters
            import re
            match = re.search(r"__doPostBack\('([^']+)','([^']*)'\)", onclick)
            if not match:
                print("❌ Could not extract postback parameters")
                return None

            event_target = match.group(1)
            event_argument = match.group(2)

            print(f"📥 Event target: {event_target}")
            print(f"📥 Event argument: {event_argument}")

            # Get current page URL and form data
            current_url = "https://www.bseindia.com/corporates/Insider_Trading_new.aspx"

            # Extract form fields from current page
            form_data = self._extract_aspnet_form_data()
            if not form_data:
                print("❌ Could not extract ASP.NET form data")
                return None

            # Set the date range for download (this is crucial!)
            self._set_date_range_for_download(form_data)

            # Override postback parameters (this will replace any existing values)
            form_data['__EVENTTARGET'] = event_target
            form_data['__EVENTARGUMENT'] = event_argument

            # Debug: Show the key form fields being submitted
            print(f"📥 Form submission details:")
            print(f"   __EVENTTARGET: {form_data.get('__EVENTTARGET', 'NOT SET')}")
            print(f"   __EVENTARGUMENT: {form_data.get('__EVENTARGUMENT', 'NOT SET')}")
            print(f"   __VIEWSTATE length: {len(form_data.get('__VIEWSTATE', ''))}")
            print(f"   __EVENTVALIDATION length: {len(form_data.get('__EVENTVALIDATION', ''))}")
            print(f"   From Date: {form_data.get('ctl00$ContentPlaceHolder1$fmdate', 'NOT SET')}")
            print(f"   To Date: {form_data.get('ctl00$ContentPlaceHolder1$eddate', 'NOT SET')}")

            # Clean up form data to remove any potential duplicates or conflicts
            cleaned_form_data = self._clean_form_data(form_data)

            # Submit the form
            print("📥 Submitting download form...")
            response = self.session_manager.make_post_request(current_url, data=cleaned_form_data)

            if response and response.status_code == 200:
                print(f"✅ Download form submitted successfully ({len(response.content)} bytes)")

                # Check if response is CSV data
                content_disposition = response.headers.get('content-disposition', '')
                content_type = response.headers.get('content-type', '').lower()

                if 'attachment' in content_disposition or 'csv' in content_type:
                    print("📥 Received CSV download")
                    return self.parse_csv_content(response.text)
                else:
                    # Try parsing as CSV anyway
                    csv_result = self.parse_csv_content(response.text)
                    if csv_result is not None:
                        return csv_result

                    # If not CSV, might be HTML with updated data
                    print("📥 Received HTML response, parsing...")
                    return self.parse_html_content(response.text)
            else:
                print(f"❌ Download form submission failed: {response.status_code if response else 'None'}")
                return None

        except Exception as e:
            print(f"Error handling postback download: {e}")
            return None

    def _extract_aspnet_form_data(self):
        """Extract ASP.NET form data from current page"""
        try:
            form_data = {}

            # Extract viewstate
            viewstate = self.soup.find('input', {'name': '__VIEWSTATE'})
            if viewstate:
                form_data['__VIEWSTATE'] = viewstate.get('value', '')

            # Extract viewstate generator
            viewstate_gen = self.soup.find('input', {'name': '__VIEWSTATEGENERATOR'})
            if viewstate_gen:
                form_data['__VIEWSTATEGENERATOR'] = viewstate_gen.get('value', '')

            # Extract event validation
            event_validation = self.soup.find('input', {'name': '__EVENTVALIDATION'})
            if event_validation:
                form_data['__EVENTVALIDATION'] = event_validation.get('value', '')

            # Initialize required ASP.NET fields with empty values (will be overridden for download)
            form_data['__EVENTTARGET'] = ''
            form_data['__EVENTARGUMENT'] = ''
            form_data['__VIEWSTATEENCRYPTED'] = ''

            # Extract other form fields that might be needed, but avoid duplicating the ones we just set
            form_inputs = self.soup.find_all('input', {'type': ['hidden', 'text']})
            for input_field in form_inputs:
                name = input_field.get('name', '')
                value = input_field.get('value', '')
                if name and name not in form_data and name not in ['__VIEWSTATE', '__VIEWSTATEGENERATOR', '__EVENTVALIDATION']:
                    form_data[name] = value

            # Extract select fields (dropdowns)
            select_fields = self.soup.find_all('select')
            for select_field in select_fields:
                name = select_field.get('name', '')
                if name:
                    # Get selected option value
                    selected_option = select_field.find('option', {'selected': True})
                    if selected_option:
                        form_data[name] = selected_option.get('value', '')
                    else:
                        # Get first option if no selected option
                        first_option = select_field.find('option')
                        if first_option:
                            form_data[name] = first_option.get('value', '')

            # Add common BSE form fields with default values if not already present
            default_fields = {
                'ctl00$ContentPlaceHolder1$fmdate': '',
                'ctl00$ContentPlaceHolder1$eddate': '',
                'ctl00$ContentPlaceHolder1$hidCurrentDate': '',
                'ctl00$ContentPlaceHolder1$txtDate': '',
                'ctl00$ContentPlaceHolder1$txtTodate': '',
                'ctl00$ContentPlaceHolder1$hf_scripcode': '',
                'ctl00$ContentPlaceHolder1$SmartSearch$hdnCode': '',
                'ctl00$ContentPlaceHolder1$SmartSearch$smartSearch': '',
                'ctl00$ContentPlaceHolder1$ctl00_ContentPlaceHolder1_hdnCode': '',
                'ctl00$ContentPlaceHolder1$rdoMode': '0',
                'ddlPeriod': 'Day',
                'ddlRange': '1M',
                'txtScripCode': '',
                'txtCompany': '',
                'txtName': ''
            }

            for field_name, default_value in default_fields.items():
                if field_name not in form_data:
                    form_data[field_name] = default_value

            print(f"📥 Extracted {len(form_data)} form fields")
            return form_data

        except Exception as e:
            print(f"Error extracting ASP.NET form data: {e}")
            return None

    def _clean_form_data(self, form_data):
        """Clean form data to remove duplicates and ensure proper formatting"""
        try:
            cleaned_data = {}

            # Process each field only once, keeping the last value
            for key, value in form_data.items():
                if key in cleaned_data:
                    print(f"⚠️ Duplicate form field detected: {key}")
                    print(f"   Old value: {cleaned_data[key]}")
                    print(f"   New value: {value}")

                # Always use the latest value (this handles the __EVENTTARGET override)
                cleaned_data[key] = value

            # Ensure critical fields are properly set
            critical_fields = ['__EVENTTARGET', '__EVENTARGUMENT', '__VIEWSTATE', '__EVENTVALIDATION']
            for field in critical_fields:
                if field not in cleaned_data:
                    cleaned_data[field] = ''
                    print(f"⚠️ Missing critical field {field}, set to empty string")

            print(f"📥 Cleaned form data: {len(cleaned_data)} unique fields")
            return cleaned_data

        except Exception as e:
            print(f"Error cleaning form data: {e}")
            return form_data  # Return original data if cleaning fails

    def _set_date_range_for_download(self, form_data):
        """Set the proper date range for BSE download based on the original query"""
        try:
            from datetime import datetime, timedelta

            # Use the date range passed to the parser, or default to last 7 days
            if self.start_date and self.end_date:
                start_date = self.start_date
                end_date = self.end_date
                print(f"📅 Using parser date range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
            else:
                # Check if dates are already set in the form
                existing_from = form_data.get('ctl00$ContentPlaceHolder1$fmdate', '')
                existing_to = form_data.get('ctl00$ContentPlaceHolder1$eddate', '')

                if existing_from and existing_to:
                    print(f"📅 Using existing date range: {existing_from} to {existing_to}")
                    return

                # If no dates set, use last 7 days as default
                end_date = datetime.now()
                start_date = end_date - timedelta(days=7)
                print(f"📅 Using default date range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")

            # Format dates for BSE (YYYYMMDD format)
            from_date_str = start_date.strftime('%Y%m%d')
            to_date_str = end_date.strftime('%Y%m%d')

            # Set the date fields
            form_data['ctl00$ContentPlaceHolder1$fmdate'] = from_date_str
            form_data['ctl00$ContentPlaceHolder1$eddate'] = to_date_str

            # Also set the text date fields (DD/MM/YYYY format)
            from_date_text = start_date.strftime('%d/%m/%Y')
            to_date_text = end_date.strftime('%d/%m/%Y')

            form_data['ctl00$ContentPlaceHolder1$txtDate'] = from_date_text
            form_data['ctl00$ContentPlaceHolder1$txtTodate'] = to_date_text

            # Set current date field
            current_date_str = end_date.strftime('%m/%d/%Y 12:00:00 AM')
            form_data['ctl00$ContentPlaceHolder1$hidCurrentDate'] = current_date_str

            print(f"📅 Set download date range: {from_date_str} to {to_date_str}")
            print(f"📅 Text date range: {from_date_text} to {to_date_text}")

        except Exception as e:
            print(f"Error setting date range for download: {e}")
            # Continue without setting dates - let BSE use defaults

    def parse_bse_table(self, table):
        """
        Parse BSE-specific table structure.
        Based on the working bsescraper.py implementation.
        """
        try:
            # Extract headers from BSE table structure
            headers = []
            header_row = table.find_all("tr", class_="innertable_header1")

            if not header_row:
                print("❌ Could not find BSE table headers")
                return None

            header_row = header_row[0]
            for th in header_row.find_all(["td", "th"]):
                if th.get("colspan") == "4":
                    headers.extend([
                        "Securities Acquired/Disposed - Type of Securities",
                        "Securities Acquired/Disposed - Number",
                        "Securities Acquired/Disposed - Value",
                        "Securities Acquired/Disposed - Transaction Type"
                    ])
                elif th.get("colspan") == "3":
                    headers.extend([
                        "Trading in Derivatives - Type of Contract",
                        "Trading in Derivatives - Buy Value (Units)",
                        "Trading in Derivatives - Sale Value (Units)"
                    ])
                else:
                    headers.append(th.text.strip())

            # Extract data rows
            data = []
            data_rows = table.find_all("tr", class_="TTRow")
            print(f"📊 Found {len(data_rows)} data rows in BSE table")

            for row in data_rows:
                cols = row.find_all("td")
                row_data = []
                for col in cols:
                    # Handle columns with nested spans (BSE specific)
                    spans = col.find_all("span")
                    if len(spans) == 2:
                        # Combine number and percentage
                        row_data.append(f"{spans[0].text.strip()} ({spans[1].text.strip()})")
                    else:
                        # Clean text and handle multiline content
                        text = col.text.strip().replace('\n', ' ').replace('\r', ' ')
                        text = ' '.join(text.split())  # Remove extra whitespace
                        row_data.append(text)
                data.append(row_data)

            # Create DataFrame
            if not data:
                print("❌ No data rows found in BSE table")
                return None

            df = pd.DataFrame(data, columns=headers)
            df = df.dropna(how='all').dropna(axis=1, how='all')

            print(f"✅ Parsed {len(df)} records from BSE table")
            return self.clean_bse_dataframe(df)

        except Exception as e:
            print(f"Error parsing BSE table: {e}")
            return None
    
    def is_insider_trading_table(self, header_text):
        """
        Check if table headers indicate insider trading data.
        Returns True if it looks like the right table.
        """
        # Keywords that indicate insider trading data
        keywords = [
            'company', 'symbol', 'insider', 'trading', 
            'acquisition', 'disposal', 'shares', 'date'
        ]
        
        # Check if enough keywords are present
        found_keywords = sum(1 for keyword in keywords if keyword in header_text)
        
        return found_keywords >= 3  # Need at least 3 keywords
    
    def extract_table_data(self, table):
        """
        Extract data from HTML table into pandas DataFrame.
        Handles headers and data rows separately.
        """
        try:
            rows = table.find_all('tr')
            
            if len(rows) < 2:
                print("Table has insufficient rows")
                return None
            
            # Extract headers from first row
            headers = self.extract_headers(rows[0])
            
            if not headers:
                print("Could not extract headers from table")
                return None
            
            # Extract data from remaining rows
            data_rows = self.extract_data_rows(rows[1:], len(headers))
            
            if not data_rows:
                print("Could not extract data rows from table")
                return None
            
            # Create DataFrame
            dataframe = pd.DataFrame(data_rows, columns=headers)
            return dataframe
            
        except Exception as e:
            print(f"Error extracting table data: {e}")
            return None
    
    def extract_headers(self, header_row):
        """Extract column headers from the first row"""
        headers = []
        cells = header_row.find_all(['th', 'td'])
        
        for cell in cells:
            header_text = cell.get_text().strip()
            if header_text:  # Only add non-empty headers
                headers.append(header_text)
        
        print(f"Extracted {len(headers)} headers")
        return headers
    
    def extract_data_rows(self, data_rows, expected_columns):
        """Extract data from table rows"""
        extracted_rows = []
        
        for row in data_rows:
            cells = row.find_all(['td', 'th'])
            
            if len(cells) == expected_columns:
                row_data = []
                for cell in cells:
                    cell_text = cell.get_text().strip()
                    row_data.append(cell_text)
                
                # Only add rows that have some actual data
                if any(cell_data for cell_data in row_data):
                    extracted_rows.append(row_data)
        
        return extracted_rows
    
    def clean_bse_dataframe(self, dataframe):
        """
        Clean and standardize BSE DataFrame.
        Handles BSE-specific data formatting and column names.
        """
        try:
            # Remove completely empty rows
            dataframe = dataframe.dropna(how='all')

            # Clean text in all columns
            for column in dataframe.columns:
                if dataframe[column].dtype == 'object':
                    # Strip whitespace and clean text
                    dataframe[column] = dataframe[column].astype(str).str.strip()
                    # Replace multiple spaces with single space
                    dataframe[column] = dataframe[column].str.replace(r'\s+', ' ', regex=True)
                    # Replace common BSE data artifacts
                    dataframe[column] = dataframe[column].str.replace('&nbsp;', ' ')
                    dataframe[column] = dataframe[column].str.replace('\xa0', ' ')

            # Remove rows where all values are empty or just whitespace
            mask = ~dataframe.apply(
                lambda x: x.astype(str).str.strip().eq('').all(),
                axis=1
            )
            dataframe = dataframe[mask]

            # Standardize column names for consistency
            dataframe.columns = dataframe.columns.str.strip()

            print(f"✅ Cleaned BSE DataFrame: {len(dataframe)} records, {len(dataframe.columns)} columns")
            return dataframe

        except Exception as e:
            print(f"Error cleaning BSE DataFrame: {e}")
            return dataframe

    def clean_dataframe(self, dataframe):
        """Legacy method for backward compatibility"""
        return self.clean_bse_dataframe(dataframe)
