"""
Main BSE Scraper Class
Orchestrates all components to provide a clean, simple interface.
Enhanced with proper BSE form handling and duplicate detection.
"""

from .session import <PERSON><PERSON>anager
from .fetcher import <PERSON>Fetcher
from .parser import BSEParser
from .file_manager import <PERSON>Manager
from .database_manager import BSEDatabaseManager
from .config import BASE_URL, DEFAULT_DAYS_BACK, SAVE_CSV, USE_DATABASE
from datetime import datetime, timedelta


class BSEScraper:
    """
    Main BSE scraper class that coordinates all operations.
    Provides simple methods for scraping insider trading data with duplicate detection.
    """

    def __init__(self):
        """Initialize all components of the scraper"""
        print("🏢 BSE Insider Trading Scraper")
        print("=" * 50)
        print("Initializing enhanced BSE scraper...")

        # Initialize all components
        self.session_manager = SessionManager()
        self.data_fetcher = DataFetcher(self.session_manager)
        self.html_parser = BSEParser(self.session_manager)  # Pass session manager to parser
        self.file_manager = FileManager()

        # Initialize database manager if enabled
        self.database_manager = None
        if USE_DATABASE:
            try:
                self.database_manager = BSEDatabaseManager()
                print("✅ Database integration enabled")
            except Exception as e:
                print(f"⚠️ Database integration failed: {e}")
                print("⚠️ Continuing with CSV-only mode")

        print("✅ BSE Scraper ready to use")
        print(f"✅ CSV saving {'enabled' if SAVE_CSV else 'disabled'}")
        print(f"✅ Database saving {'enabled' if USE_DATABASE and self.database_manager else 'disabled'}")
        print()
    
    def scrape_recent_data(self, days_back=DEFAULT_DAYS_BACK):
        """
        Scrape insider trading data for the last N days.
        Returns dictionary with results or None if failed.
        """
        try:
            print(f"📅 Scraping BSE data for the last {days_back} days...")

            # Calculate date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)

            print(f"📅 Date range: {start_date.strftime('%d/%m/%Y')} to {end_date.strftime('%d/%m/%Y')}")

            # Test connection first
            if not self.test_connection():
                return None

            # Fetch the data using proper BSE form submission
            print("🔄 Fetching data from BSE...")
            html_content = self.data_fetcher.fetch_data(start_date, end_date)

            if html_content is None:
                print("❌ Failed to fetch data from BSE")
                return None

            print("✅ Successfully fetched data from BSE")

            # Parse the HTML content
            print("🔄 Parsing BSE response...")
            dataframe = self.html_parser.parse_html_content(html_content, start_date, end_date)

            if dataframe is None or len(dataframe) == 0:
                print("❌ No data found or parsing failed")
                return None

            print(f"✅ Successfully parsed {len(dataframe)} records")

            # Validate and clean the data before processing
            print("🔍 Validating parsed data...")
            dataframe = self._validate_and_clean_dataframe(dataframe)

            if dataframe is None or len(dataframe) == 0:
                print("❌ No valid data found after validation")
                return None

            print(f"✅ Validation complete: {len(dataframe)} valid records")

            # Add record hashes for duplicate detection (needed for both CSV and database)
            from .hash_utils import add_record_hashes
            dataframe_with_hashes = add_record_hashes(dataframe.copy())

            # Save the data to CSV if enabled
            file_path = None
            if SAVE_CSV:
                print("💾 Saving BSE data to CSV...")
                file_path = self.file_manager.save_dataframe_to_csv(
                    dataframe_with_hashes,
                    start_date,
                    end_date
                )
            else:
                print("📋 CSV saving disabled")

            # Save to database if enabled
            db_result = None
            if USE_DATABASE and self.database_manager:
                print("🗄️ Saving BSE data to database...")
                # Convert DataFrame to records for database insertion
                records = dataframe_with_hashes.to_dict('records')
                db_result = self.database_manager.save_records(records)

                if db_result['success']:
                    print(f"🗄️ Database: {db_result['inserted']} new BSE records inserted")
                    if db_result['duplicates'] > 0:
                        print(f"🗄️ Database: {db_result['duplicates']} duplicates skipped")
                else:
                    print(f"❌ Database insertion failed: {db_result['errors']} errors")
            else:
                print("📋 Database saving disabled")

            # Return comprehensive result
            return {
                'dataframe': dataframe,
                'file_path': file_path,
                'start_date': start_date,
                'end_date': end_date,
                'record_count': len(dataframe),
                'column_count': len(dataframe.columns),
                'database_result': db_result
            }

        except Exception as e:
            print(f"❌ Error in scrape_recent_data: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def scrape_date_range(self, start_date_str, end_date_str):
        """
        Scrape data for a specific date range.
        Dates should be in DD/MM/YYYY format.
        """
        try:
            print(f"📅 Scraping BSE data from {start_date_str} to {end_date_str}")

            # Parse date strings
            from datetime import datetime
            start_date = datetime.strptime(start_date_str, '%d/%m/%Y')
            end_date = datetime.strptime(end_date_str, '%d/%m/%Y')

            # Test connection first
            if not self.test_connection():
                return None

            # Fetch data for the date range
            print("🔄 Fetching data from BSE...")
            html_content = self.data_fetcher.fetch_data(start_date, end_date)

            if html_content is None:
                print("❌ Failed to fetch data from BSE")
                return None

            print("✅ Successfully fetched data from BSE")

            # Parse the HTML content
            print("🔄 Parsing BSE response...")
            dataframe = self.html_parser.parse_html_content(html_content, start_date, end_date)

            if dataframe is None or len(dataframe) == 0:
                print("❌ No data found or parsing failed")
                return None

            print(f"✅ Successfully parsed {len(dataframe)} records")

            # Add record hashes for duplicate detection (needed for both CSV and database)
            from .hash_utils import add_record_hashes
            dataframe_with_hashes = add_record_hashes(dataframe.copy())

            # Save the data to CSV if enabled
            file_path = None
            if SAVE_CSV:
                print("💾 Saving BSE data to CSV...")
                file_path = self.file_manager.save_dataframe_to_csv(
                    dataframe_with_hashes,
                    start_date,
                    end_date
                )
            else:
                print("📋 CSV saving disabled")

            # Save to database if enabled
            db_result = None
            if USE_DATABASE and self.database_manager:
                print("🗄️ Saving BSE data to database...")
                # Convert DataFrame to records for database insertion
                records = dataframe_with_hashes.to_dict('records')
                db_result = self.database_manager.save_records(records)

                if db_result['success']:
                    print(f"🗄️ Database: {db_result['inserted']} new BSE records inserted")
                    if db_result['duplicates'] > 0:
                        print(f"🗄️ Database: {db_result['duplicates']} duplicates skipped")
                else:
                    print(f"❌ Database insertion failed: {db_result['errors']} errors")
            else:
                print("📋 Database saving disabled")

            # Return comprehensive result
            return {
                'dataframe': dataframe,
                'file_path': file_path,
                'start_date': start_date,
                'end_date': end_date,
                'record_count': len(dataframe),
                'column_count': len(dataframe.columns),
                'database_result': db_result
            }

        except Exception as e:
            print(f"❌ Error in scrape_date_range: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def test_connection(self):
        """Test connection to BSE website"""
        print("🔍 Testing connection to BSE website...")
        success = self.session_manager.test_connection(BASE_URL)
        if success:
            print("✅ BSE connection test successful")
        else:
            print("❌ BSE connection test failed")
        return success
    
    def get_data_summary(self, dataframe):
        """
        Get a summary of the scraped data.
        Returns dictionary with useful statistics.
        """
        if dataframe is None or len(dataframe) == 0:
            return {
                'total_records': 0,
                'columns': [],
                'column_count': 0
            }
        
        summary = {
            'total_records': len(dataframe),
            'columns': dataframe.columns.tolist(),
            'column_count': len(dataframe.columns)
        }
        
        # Try to find company information
        company_columns = [col for col in dataframe.columns 
                          if 'company' in col.lower() or 'name' in col.lower()]
        
        if company_columns:
            company_col = company_columns[0]
            unique_companies = dataframe[company_col].unique()
            summary['unique_companies'] = len(unique_companies)
            summary['sample_companies'] = unique_companies[:5].tolist()
        
        return summary
    
    def list_saved_files(self):
        """List all files saved by the scraper"""
        return self.file_manager.list_saved_files()
    
    def save_custom_filename(self, dataframe, start_date, end_date, filename):
        """
        Save dataframe with a custom filename.
        Useful for specific naming requirements.
        """
        return self.file_manager.save_dataframe_to_csv(
            dataframe, start_date, end_date, filename
        )
    
    def close(self):
        """Close the scraper and clean up resources"""
        self.session_manager.close_session()

    def _validate_and_clean_dataframe(self, dataframe):
        """
        Validate and clean the parsed DataFrame to remove malformed records.

        Args:
            dataframe: pandas DataFrame with parsed BSE data

        Returns:
            pandas DataFrame with validated and cleaned data
        """
        try:
            if dataframe is None or dataframe.empty:
                return dataframe

            import pandas as pd

            original_count = len(dataframe)
            print(f"🔍 Validating {original_count} parsed records...")

            # Check which required columns exist in the dataframe
            existing_columns = dataframe.columns.tolist()
            print(f"📋 Available columns: {existing_columns}")

            # Create a mask for valid records
            valid_mask = pd.Series([True] * len(dataframe), index=dataframe.index)

            # Check Security Code
            if 'Security Code' in dataframe.columns:
                print(f"🔍 Checking 'Security Code' column - sample values: {dataframe['Security Code'].head(3).tolist()}")
                security_code_valid = (
                    dataframe['Security Code'].notna() &
                    (dataframe['Security Code'].astype(str).str.strip() != '') &
                    (dataframe['Security Code'].astype(str).str.strip() != 'nan')
                )
                print(f"🔍 'Security Code' valid count: {security_code_valid.sum()}/{len(security_code_valid)}")
                valid_mask = valid_mask & security_code_valid
                invalid_security_count = (~security_code_valid).sum()
                if invalid_security_count > 0:
                    print(f"⚠️ Found {invalid_security_count} records with invalid Security Code")

            # Check Security Name
            if 'Security Name' in dataframe.columns:
                print(f"🔍 Checking 'Security Name' column - sample values: {dataframe['Security Name'].head(3).tolist()}")
                security_name_valid = (
                    dataframe['Security Name'].notna() &
                    (dataframe['Security Name'].astype(str).str.strip() != '') &
                    (dataframe['Security Name'].astype(str).str.strip() != 'nan')
                )
                print(f"🔍 'Security Name' valid count: {security_name_valid.sum()}/{len(security_name_valid)}")
                valid_mask = valid_mask & security_name_valid
                invalid_name_count = (~security_name_valid).sum()
                if invalid_name_count > 0:
                    print(f"⚠️ Found {invalid_name_count} records with invalid Security Name")

            # Check Person Name (try both column name variations)
            person_name_valid = pd.Series([False] * len(dataframe), index=dataframe.index)

            if 'Name of Person' in dataframe.columns:
                print(f"🔍 Checking 'Name of Person' column - sample values: {dataframe['Name of Person'].head(3).tolist()}")
                col_valid = (
                    dataframe['Name of Person'].notna() &
                    (dataframe['Name of Person'].astype(str).str.strip() != '') &
                    (dataframe['Name of Person'].astype(str).str.strip() != 'nan')
                )
                print(f"🔍 'Name of Person' valid count: {col_valid.sum()}/{len(col_valid)}")
                person_name_valid = person_name_valid | col_valid

            if 'Name of  Person' in dataframe.columns:  # Double space version
                print(f"🔍 Checking 'Name of  Person' column - sample values: {dataframe['Name of  Person'].head(3).tolist()}")
                col_valid = (
                    dataframe['Name of  Person'].notna() &
                    (dataframe['Name of  Person'].astype(str).str.strip() != '') &
                    (dataframe['Name of  Person'].astype(str).str.strip() != 'nan')
                )
                print(f"🔍 'Name of  Person' valid count: {col_valid.sum()}/{len(col_valid)}")
                person_name_valid = person_name_valid | col_valid

            valid_mask = valid_mask & person_name_valid
            invalid_person_count = (~person_name_valid).sum()
            if invalid_person_count > 0:
                print(f"⚠️ Found {invalid_person_count} records with invalid Person Name")

            # Check Person Category (try multiple column name variations)
            person_category_valid = pd.Series([False] * len(dataframe), index=dataframe.index)

            category_columns = [
                'Category of Person',
                'Category of Person *',  # Asterisk version
                'Category of person',    # Lowercase version
                'Category of person *'   # Lowercase with asterisk
            ]

            for col in category_columns:
                if col in dataframe.columns:
                    print(f"🔍 Checking category column '{col}' - sample values: {dataframe[col].head(3).tolist()}")
                    col_valid = (
                        dataframe[col].notna() &
                        (dataframe[col].astype(str).str.strip() != '') &
                        (dataframe[col].astype(str).str.strip() != 'nan')
                    )
                    print(f"🔍 Column '{col}' valid count: {col_valid.sum()}/{len(col_valid)}")
                    person_category_valid = person_category_valid | col_valid

            valid_mask = valid_mask & person_category_valid
            invalid_category_count = (~person_category_valid).sum()
            if invalid_category_count > 0:
                print(f"⚠️ Found {invalid_category_count} records with invalid Person Category")

            # Filter the dataframe to keep only valid records
            cleaned_dataframe = dataframe[valid_mask].copy()

            # Additional cleaning: remove rows where all important fields are empty
            important_columns = [col for col in existing_columns if col in [
                'Security Code', 'Security Name', 'Name of Person', 'Name of  Person',
                'Category of Person', 'Category of Person *', 'Category of person', 'Category of person *',
                'Securities Acquired/Disposed - Number'
            ]]

            if important_columns:
                # Remove rows where all important columns are empty or just whitespace
                non_empty_mask = cleaned_dataframe[important_columns].apply(
                    lambda row: row.astype(str).str.strip().ne('').any(), axis=1
                )
                cleaned_dataframe = cleaned_dataframe[non_empty_mask].copy()

            removed_count = original_count - len(cleaned_dataframe)
            if removed_count > 0:
                print(f"🧹 Removed {removed_count} invalid/malformed records")
                print(f"✅ Kept {len(cleaned_dataframe)} valid records")
            else:
                print(f"✅ All {len(cleaned_dataframe)} records are valid")

            return cleaned_dataframe

        except Exception as e:
            print(f"❌ Error during data validation: {e}")
            import traceback
            traceback.print_exc()
            return dataframe  # Return original dataframe if validation fails
        print("BSE Scraper closed")
    
    def __enter__(self):
        """Support for context manager (with statement)"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Cleanup when exiting context manager"""
        self.close()
