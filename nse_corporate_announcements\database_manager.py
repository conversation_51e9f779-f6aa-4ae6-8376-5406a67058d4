"""
Database Manager for NSE Corporate Announcements Scraper
Handles Supabase database operations for corporate announcements data.
"""

import requests
import json
import csv
import os
from datetime import datetime
from .config import SUPABASE_URL, SUPABASE_ANON_KEY, DATABASE_TABLE
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from shared.utils import calculate_unified_hash


class NSECorporateAnnouncementsDatabaseManager:
    """Manages Supabase database operations for NSE corporate announcements data"""

    def __init__(self):
        """Initialize database manager for NSE corporate announcements data."""
        self.supabase_url = SUPABASE_URL
        self.supabase_key = SUPABASE_ANON_KEY
        self.headers = {
            'apikey': self.supabase_key,
            'Authorization': f'Bearer {self.supabase_key}',
            'Content-Type': 'application/json',
            'Prefer': 'return=minimal'
        }
        self.table_name = DATABASE_TABLE

    def normalize_null_values(self, value):
        """
        Normalize field values by converting string representations of null values to actual Python None.
        
        Args:
            value: The value to normalize
            
        Returns:
            The normalized value (None if it represents null, otherwise the original value)
        """
        if value is None:
            return None
        
        # Convert to string for comparison
        str_value = str(value).strip().lower()
        
        # List of string representations that should be treated as null
        null_representations = [
            '', 'null', 'none', 'n/a', 'na', 'nil', 'undefined', 
            'empty', '-', '--', '---', 'not available', 'not applicable'
        ]
        
        if str_value in null_representations:
            return None
            
        return value

    def _convert_csv_to_db_columns(self, csv_row):
        """
        Convert CSV column names to database column names for corporate announcements

        Args:
            csv_row (dict): Row with CSV column names

        Returns:
            dict: Row with database column names
        """
        def parse_datetime(date_str):
            """Parse datetime string to ISO format with enhanced null handling"""
            # Use the enhanced null normalization
            normalized_value = self.normalize_null_values(date_str)
            if normalized_value is None:
                return None

            try:
                # Handle different datetime formats
                if isinstance(normalized_value, str):
                    # Try parsing DD-MMM-YYYY HH:MM:SS format
                    if '-' in normalized_value and ':' in normalized_value:
                        dt = datetime.strptime(normalized_value, '%d-%b-%Y %H:%M:%S')
                        return dt.isoformat()
                return str(normalized_value)
            except Exception:
                # If parsing fails, return the original normalized value
                return str(normalized_value) if normalized_value else None

        def parse_file_size(file_size_str):
            """Parse file size string to standardized format with enhanced null handling"""
            # Use the enhanced null normalization
            normalized_value = self.normalize_null_values(file_size_str)
            if normalized_value is None:
                return None

            try:
                # Handle formats like "9 KB", "1.5 MB", etc.
                return str(normalized_value).strip()
            except Exception:
                return None

        def parse_difference_seconds(diff_str):
            """Parse time difference string to total seconds with enhanced null handling"""
            # Use the enhanced null normalization
            normalized_value = self.normalize_null_values(diff_str)
            if normalized_value is None:
                return None

            try:
                # Handle formats like "00:00:01", "00:01:30", etc.
                if isinstance(normalized_value, str) and ':' in normalized_value:
                    parts = normalized_value.split(':')
                    if len(parts) == 3:  # HH:MM:SS format
                        hours = int(parts[0])
                        minutes = int(parts[1])
                        seconds = int(parts[2])
                        total_seconds = hours * 3600 + minutes * 60 + seconds
                        return total_seconds
                return None
            except Exception:
                return None

        # Map the filtered CSV columns to database columns with enhanced null normalization
        db_row = {
            'symbol': self.normalize_null_values(csv_row.get('symbol', '')),
            'company_name': self.normalize_null_values(csv_row.get('sm_name', '')),
            'subject': self.normalize_null_values(csv_row.get('desc', '')),
            'details': self.normalize_null_values(csv_row.get('attchmnttext', '')),
            'broadcast_date_time': parse_datetime(csv_row.get('exchdisstime')),
            'receipt': parse_datetime(csv_row.get('an_dt')),
            'dissemination': parse_datetime(csv_row.get('exchdisstime')),
            'difference_seconds': parse_difference_seconds(csv_row.get('difference')),
            'attachment_url': self.normalize_null_values(csv_row.get('attchmntfile', '')),
            'file_size': parse_file_size(csv_row.get('filesize', csv_row.get('fileSize'))),
            'record_hash': self.normalize_null_values(csv_row.get('record_hash', ''))
        }

        return db_row

    def insert_records(self, records):
        """
        Insert corporate announcements records into the database with duplicate detection.

        Args:
            records (list): List of record dictionaries to insert

        Returns:
            dict: Summary of insertion results
        """
        try:
            if not records:
                return {'success': True, 'inserted': 0, 'duplicates': 0, 'errors': 0}
            
            print(f"Processing {len(records)} corporate announcements records for database insertion...")

            # Convert CSV format to database format and generate hashes
            db_records = []
            for record in records:
                db_record = self._convert_csv_to_db_columns(record)
                db_records.append(db_record)

            # Check for existing records to avoid duplicates
            print("🔍 Checking for existing records in database...")
            existing_hashes = self._get_existing_hashes()
            
            new_records = []
            duplicate_records = []
            duplicate_count = 0

            for record in db_records:
                record_hash = record.get('record_hash')
                if record_hash and record_hash in existing_hashes:
                    duplicate_count += 1
                    duplicate_records.append(record)
                else:
                    new_records.append(record)

            if duplicate_count > 0:
                print(f"🔍 Found {duplicate_count} duplicate corporate announcements records in database")

            if not new_records:
                print(f"ℹ️ No new corporate announcements records to insert (all were duplicates)")
                return {'success': True, 'inserted': 0, 'duplicates': duplicate_count, 'errors': 0}

            # Insert new records
            print(f"💾 Inserting {len(new_records)} new corporate announcements records to database...")

            total_inserted = 0
            total_errors = 0

            # Insert records in batches
            batch_size = 50
            for i in range(0, len(new_records), batch_size):
                batch = new_records[i:i + batch_size]
                
                try:
                    success = self._insert_batch(batch)
                    if success:
                        total_inserted += len(batch)
                        print(f"✅ Inserted batch {i//batch_size + 1}: {len(batch)} records")
                    else:
                        total_errors += len(batch)
                        print(f"❌ Failed to insert batch {i//batch_size + 1}")
                        
                except Exception as e:
                    print(f"❌ Error inserting batch {i//batch_size + 1}: {e}")
                    total_errors += len(batch)

            print(f"📊 Database insertion summary:")
            print(f"   ✅ Successfully inserted: {total_inserted}")
            print(f"   🔍 Duplicates skipped: {duplicate_count}")
            print(f"   ❌ Errors: {total_errors}")

            return {
                'success': total_errors == 0,
                'inserted': total_inserted,
                'duplicates': duplicate_count,
                'errors': total_errors
            }

        except Exception as e:
            print(f"❌ Error in database insertion: {e}")
            return {'success': False, 'inserted': 0, 'duplicates': 0, 'errors': len(records)}

    def _get_existing_hashes(self):
        """Get set of existing record hashes from database"""
        try:
            url = f"{self.supabase_url}/rest/v1/{self.table_name}"
            params = {'select': 'record_hash'}

            response = requests.get(url, headers=self.headers, params=params, timeout=30)

            if response.status_code == 200:
                data = response.json()
                return {record.get('record_hash') for record in data if record.get('record_hash')}
            else:
                print(f"⚠️ Failed to fetch existing hashes: {response.status_code}")
                return set()

        except Exception as e:
            print(f"⚠️ Error fetching existing hashes: {e}")
            return set()

    def _insert_batch(self, records_batch):
        """Insert a batch of records"""
        try:
            url = f"{self.supabase_url}/rest/v1/{self.table_name}"
            
            response = requests.post(
                url,
                headers=self.headers,
                json=records_batch,
                timeout=60
            )
            
            return response.status_code in [200, 201]
            
        except Exception as e:
            print(f"❌ Error inserting batch: {e}")
            return False

    def test_connection(self):
        """Test database connection"""
        try:
            url = f"{self.supabase_url}/rest/v1/{self.table_name}"
            params = {'select': 'count', 'limit': '1'}
            
            response = requests.get(url, headers=self.headers, params=params, timeout=10)
            
            if response.status_code == 200:
                print("✅ Database connection successful")
                return True
            else:
                print(f"❌ Database connection failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Database connection error: {e}")
            return False
