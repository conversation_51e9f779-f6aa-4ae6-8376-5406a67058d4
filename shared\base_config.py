"""
Base configuration class for shared settings across NSE and BSE scrapers.

This module provides a common configuration pattern that both scrapers can inherit from
while maintaining their exchange-specific settings.
"""

import os
from abc import ABC, abstractmethod

# Try to import proxy configuration
try:
    import sys
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from proxy_config import get_proxy_config, is_proxy_enabled, get_proxy_mode
    PROXY_CONFIG_AVAILABLE = True
except ImportError:
    PROXY_CONFIG_AVAILABLE = False
    print("Warning: proxy_config.py not found. Using default proxy settings.")


class BaseConfig(ABC):
    """
    Base configuration class that defines common settings and patterns
    for both NSE and BSE scrapers.
    """
    
    # Common timeout settings
    REQUEST_TIMEOUT = 30
    DEFAULT_DAYS_BACK = 1
    
    # Common file settings
    CSV_EXTENSION = '.csv'
    HTML_EXTENSION = '.html'
    FILENAME_DATE_FORMAT = '%Y%m%d_%H%M%S'
    
    # Common database settings (Supabase)
    SUPABASE_URL = "https://ratzkumhtswilixzfcgl.supabase.co"
    SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJhdHprdW1odHN3aWxpeHpmY2dsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE2MzQwNDEsImV4cCI6MjA2NzIxMDA0MX0.hvp_Oc43h2gDqozorKFVYYDgqJ5c_qIFaZK9TiHILJQ"
    
    # Common feature flags
    SAVE_CSV = True
    USE_DATABASE = True

    # ScraperAPI proxy settings
    USE_PROXY = True  # Enable/disable proxy usage
    SCRAPERAPI_KEY = "********************************"
    SCRAPERAPI_ENDPOINT = "http://api.scraperapi.com"
    SCRAPERAPI_PROXY_HOST = "proxy-server.scraperapi.com"
    SCRAPERAPI_PROXY_PORT = 8001
    SCRAPERAPI_USERNAME = "scraperapi"

    # ScraperAPI parameters for enhanced scraping
    SCRAPERAPI_RENDER_JS = True  # Enable JavaScript rendering
    SCRAPERAPI_COUNTRY_CODE = "IN"  # Use Indian proxies for better access
    SCRAPERAPI_PREMIUM = False  # Use premium proxies if needed

    def __init__(self):
        """Initialize base configuration"""
        self._load_proxy_config()
        self._validate_config()

    def _load_proxy_config(self):
        """Load proxy configuration from proxy_config.py if available"""
        if PROXY_CONFIG_AVAILABLE:
            try:
                proxy_config = get_proxy_config()
                self.USE_PROXY = proxy_config.get("USE_PROXY", self.USE_PROXY)
                self.SCRAPERAPI_KEY = proxy_config.get("API_KEY", self.SCRAPERAPI_KEY)
                self.SCRAPERAPI_ENDPOINT = proxy_config.get("ENDPOINT", self.SCRAPERAPI_ENDPOINT)
                self.SCRAPERAPI_PROXY_HOST = proxy_config.get("PROXY_HOST", self.SCRAPERAPI_PROXY_HOST)
                self.SCRAPERAPI_PROXY_PORT = proxy_config.get("PROXY_PORT", self.SCRAPERAPI_PROXY_PORT)
                self.SCRAPERAPI_RENDER_JS = proxy_config.get("RENDER_JS", self.SCRAPERAPI_RENDER_JS)
                self.SCRAPERAPI_COUNTRY_CODE = proxy_config.get("COUNTRY_CODE", self.SCRAPERAPI_COUNTRY_CODE)
                self.SCRAPERAPI_PREMIUM = proxy_config.get("PREMIUM", self.SCRAPERAPI_PREMIUM)
                self.PROXY_MODE = get_proxy_mode()
                print(f"Loaded proxy configuration: USE_PROXY={self.USE_PROXY}, MODE={self.PROXY_MODE}")
            except Exception as e:
                print(f"Warning: Failed to load proxy configuration: {e}")
        else:
            self.PROXY_MODE = "proxy"  # Default mode
    
    @property
    @abstractmethod
    def BASE_URL(self):
        """Base URL for the exchange - must be implemented by subclasses"""
        pass
    
    @property
    @abstractmethod
    def EXCHANGE_NAME(self):
        """Name of the exchange - must be implemented by subclasses"""
        pass
    
    @property
    @abstractmethod
    def OUTPUT_FOLDER(self):
        """Output folder for CSV files - must be implemented by subclasses"""
        pass
    
    @property
    @abstractmethod
    def TABLE_NAME(self):
        """Database table name - must be implemented by subclasses"""
        pass
    
    def _validate_config(self):
        """Validate configuration settings"""
        if self.REQUEST_TIMEOUT <= 0:
            raise ValueError("REQUEST_TIMEOUT must be positive")
        
        if self.DEFAULT_DAYS_BACK <= 0:
            raise ValueError("DEFAULT_DAYS_BACK must be positive")
    
    def get_output_path(self, filename):
        """
        Get full output path for a file.
        
        Args:
            filename (str): Name of the file
            
        Returns:
            str: Full path to the file
        """
        return os.path.join(self.OUTPUT_FOLDER, filename)
    
    def get_database_headers(self):
        """
        Get headers for database API requests.
        
        Returns:
            dict: Headers for Supabase API requests
        """
        return {
            'apikey': self.SUPABASE_ANON_KEY,
            'Authorization': f'Bearer {self.SUPABASE_ANON_KEY}',
            'Content-Type': 'application/json',
            'Prefer': 'return=minimal'
        }
    
    def get_database_url(self, endpoint=""):
        """
        Get full database URL for API requests.

        Args:
            endpoint (str): API endpoint to append

        Returns:
            str: Full database URL
        """
        base_url = f"{self.SUPABASE_URL}/rest/v1/{self.TABLE_NAME}"
        if endpoint:
            return f"{base_url}/{endpoint}"
        return base_url

    def get_scraperapi_url(self, target_url):
        """
        Get ScraperAPI endpoint URL with target URL and parameters.

        Args:
            target_url (str): The URL to scrape through ScraperAPI

        Returns:
            str: Complete ScraperAPI URL with parameters
        """
        if not self.USE_PROXY:
            return target_url

        params = [
            f"api_key={self.SCRAPERAPI_KEY}",
            f"url={target_url}"
        ]

        if self.SCRAPERAPI_RENDER_JS:
            params.append("render=true")

        if self.SCRAPERAPI_COUNTRY_CODE:
            params.append(f"country_code={self.SCRAPERAPI_COUNTRY_CODE}")

        if self.SCRAPERAPI_PREMIUM:
            params.append("premium=true")

        return f"{self.SCRAPERAPI_ENDPOINT}?{'&'.join(params)}"

    def get_proxy_config(self):
        """
        Get proxy configuration for requests session.

        Returns:
            dict: Proxy configuration or None if proxy is disabled
        """
        if not self.USE_PROXY or getattr(self, 'PROXY_MODE', 'proxy') == 'endpoint':
            return None

        # Build username with parameters for proxy mode
        username_parts = [self.SCRAPERAPI_USERNAME]

        if self.SCRAPERAPI_RENDER_JS:
            username_parts.append("render=true")

        if self.SCRAPERAPI_COUNTRY_CODE:
            username_parts.append(f"country_code={self.SCRAPERAPI_COUNTRY_CODE}")

        if self.SCRAPERAPI_PREMIUM:
            username_parts.append("premium=true")

        username = ".".join(username_parts)
        proxy_url = f"http://{username}:{self.SCRAPERAPI_KEY}@{self.SCRAPERAPI_PROXY_HOST}:{self.SCRAPERAPI_PROXY_PORT}"

        return {
            'http': proxy_url,
            'https': proxy_url
        }

    def should_use_endpoint_mode(self):
        """
        Check if we should use ScraperAPI endpoint mode instead of proxy mode.

        Returns:
            bool: True if endpoint mode should be used
        """
        return self.USE_PROXY and getattr(self, 'PROXY_MODE', 'proxy') == 'endpoint'
    
    def __str__(self):
        """String representation of configuration"""
        return f"{self.EXCHANGE_NAME}Config(base_url={self.BASE_URL}, output_folder={self.OUTPUT_FOLDER})"
    
    def __repr__(self):
        """Detailed representation of configuration"""
        return (f"{self.__class__.__name__}("
                f"exchange={self.EXCHANGE_NAME}, "
                f"base_url={self.BASE_URL}, "
                f"output_folder={self.OUTPUT_FOLDER}, "
                f"table_name={self.TABLE_NAME})")


class NSEConfig(BaseConfig):
    """NSE-specific configuration"""
    
    @property
    def BASE_URL(self):
        return "https://www.nseindia.com"
    
    @property
    def EXCHANGE_NAME(self):
        return "NSE"
    
    @property
    def OUTPUT_FOLDER(self):
        return 'nse_data'
    
    @property
    def TABLE_NAME(self):
        return 'nse_insider_trading'
    
    # NSE-specific settings
    API_ENDPOINT = "/api/corporates-pit"
    DEFAULT_INDEX = "equities"
    USE_CSV_FORMAT = True


class BSEConfig(BaseConfig):
    """BSE-specific configuration"""
    
    @property
    def BASE_URL(self):
        return "https://www.bseindia.com"
    
    @property
    def EXCHANGE_NAME(self):
        return "BSE"
    
    @property
    def OUTPUT_FOLDER(self):
        return 'bse_data'
    
    @property
    def TABLE_NAME(self):
        return 'bse_insider_trading'
    
    # BSE-specific settings
    INSIDER_TRADING_URL = "/corporates/Insider_Trading_new.aspx"
    BSE_DATE_FORMAT = '%d/%m/%Y'  # BSE expects DD/MM/YYYY format
    BSE_INTERNAL_DATE_FORMAT = '%Y%m%d'  # BSE internal format for form submission
